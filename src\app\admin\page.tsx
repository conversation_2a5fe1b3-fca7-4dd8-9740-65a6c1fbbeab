import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import db from "@/lib/db";


	async function getSales() {
		const data = await db.order.aggregate({
			_sum: {
				pricePaidInCents: true,
			},
			_count: {
				_id: true,
			},
		})

		return { amount: (data._sum.pricePaidInCents || 0) / 100, numberOfSales: data._count._id }
	}

export default async function Page() {

	const sales = await getSales()

  return <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
    <DashboardCard title="Products" description="Total number of products" text="10" />
    <DashboardCard title="Users" description="Total number of users" text="10" />
    <DashboardCard title="Orders" description="Total number of orders" text="10" />
	<DashboardCard title="Sales" description={`Total number of sales (${sales.numberOfSales})`} text={`${sales.amount}€`} />
  </div>
}

type DashboardCardProps = {
    title: string;
    description: string;
    text: string;
}

function DashboardCard({title, description, text}:DashboardCardProps){

    return (
			<Card>
				<CardHeader>
					<CardTitle>{title}</CardTitle>
					<CardDescription>{description}</CardDescription>
				</CardHeader>
				<CardContent>
					<p>{text}</p>
				</CardContent>
			</Card>
		)
}