// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Product {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId
  name     String
  priceInCents Int
  filePath String
  description String
  imagePath String
  isAvailableForPurchase Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orders   Order[]
  downloadVerifications DownloadVerification[]
}

model User {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId
  email    String    @unique
  name     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orders   Order[]
}

model Order {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  product  Product   @relation(fields: [productId], references: [id], onDelete: Restrict)
  userId   String    @db.ObjectId
  productId String    @db.ObjectId
  pricePaidInCents Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model DownloadVerification {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId

  pricePaidInCents Int

  expirationDate DateTime

  createdAt DateTime @default(now())

  productId String    @db.ObjectId

  product  Product   @relation(fields: [productId], references: [id], onDelete: Cascade)

  }